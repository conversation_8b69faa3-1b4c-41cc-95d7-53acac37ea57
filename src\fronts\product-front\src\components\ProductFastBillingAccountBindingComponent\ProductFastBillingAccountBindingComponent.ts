import {Component, Vue, Watch} from "vue-facing-decorator";
import {
    DropdownComponent,
    DropdownValue,
    FormModalOrDrawerComponent,
    TablePagination, ToggleComponent
} from "@groupk/vue3-interface-sdk";
import {
    AutoWired, QueryFilterGroupClause,
    QueryOperator,
    TypedQuerySearch,
    Uuid,
    VisualScopedUuid
} from "@groupk/horizon2-core";
import {ProductRepository} from "../../../../../shared/repositories/ProductRepository";
import {
    ApplicationPermission,
    BillingAccountApiIn,
    BillingAccountApiOut, EstablishmentAccountPermissionModel,
    ProductApiOut, ProductBillingAccountHttpContract, ProductHttpProductContract,
    ProductRevisionNotDetailedApiOut, ProductRevisionSearchConfig, ProductType,
} from "@groupk/mastodon-core";
import {AppState} from "../../../../../shared/AppState";
import {BillingAccountRepository} from "../../../../../shared/repositories/BillingAccountRepository";
import {BillingAccountUtils} from "../../../../../shared/utils/BillingAccountUtils";
import ProductImageComponent from "../ProductImageComponent/ProductImageComponent.vue";
import {UuidScopeProductProductRevision} from "@groupk/mastodon-core";
import {TypedQueryFilterGroup} from "@groupk/horizon2-core";
import BillingAccountFormComponent from "./BillingAccountFormComponent/BillingAccountFormComponent.vue";

export function ProductFastBillingAccountBindingComponentHasRequiredPermissions(ownedPermissions: ApplicationPermission[]) {
    return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
        ProductHttpProductContract.list,
        ProductHttpProductContract.searchRevision,
        ProductHttpProductContract.searchRevisionCount,
        ProductBillingAccountHttpContract.set
    ]);
}

@Component({
    components: {
        'form-modal-or-drawer': FormModalOrDrawerComponent,
        'product-image': ProductImageComponent,
        'dropdown': DropdownComponent,
        'toggle': ToggleComponent,
        'billing-account-form': BillingAccountFormComponent
    }
})
export default class ProductFastBillingAccountBindingComponent extends Vue {

    products: ProductApiOut[] = [];
    productRevisions: ProductRevisionNotDetailedApiOut[] = [];

    billingAccounts: Map<string, BillingAccountApiOut> = new Map();
    productBillingAccounts: Map<string, string> = new Map();

    lastKeyboardEvent: KeyboardEvent|null = null;

    missingOnly: boolean = true;

    loading: boolean = true;
    opened: boolean = false;
    saving: boolean = false;
    showPasteModal: boolean = false;
    showCustomValueModal: ProductRevisionNotDetailedApiOut|null = null;
    pastedData: string = '';
    pasteImportError: string|null = null;
    search: string = '';
    error: string|null = null;

    pagination: TablePagination = {
        totalResults: 0,
        resultsPerPage: 150,
        currentPage: 1,
        estimateTotal: false
    }

    @AutoWired(ProductRepository) accessor productRepository!: ProductRepository;
    @AutoWired(BillingAccountRepository) accessor billingAccountRepository!: BillingAccountRepository;
    @AutoWired(AppState) accessor appState!: AppState;

    async mounted() {
        setTimeout(() => this.opened = true, 0);

        const billingAccounts = await BillingAccountUtils.getAllBillingAccounts();
        this.billingAccounts = new Map(billingAccounts.map(account => [account.targetUid, account]));

        this.products = (await this.productRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();
        await this.searchProductRevisions();

        window.addEventListener('keydown', this.handleKeyDown);
        window.addEventListener('keyup', this.handleKeyUp);

        this.loading = false;
    }

    unmounted() {
        window.removeEventListener('keydown', this.handleKeyDown);
        window.removeEventListener('keyup', this.handleKeyUp);
    }

    async searchProductRevisions(cursor: {
        after?: Uuid,
        before?: Uuid
    } | null = null) {
        this.loading = true;

        const filters: TypedQuerySearch<typeof ProductRevisionSearchConfig> = {};

        const filter: TypedQueryFilterGroup<(typeof ProductRevisionSearchConfig)['filters']> = {
            group: QueryFilterGroupClause.AND,
            filters: [{
                name: 'type',
                value: ProductType.FOOD_INGREDIENT,
                operator: QueryOperator.DIFFERENT
            }]
        }

        if(this.search.length > 0) {
            filter.filters.push({
                name: 'name',
                value: this.search,
                operator: QueryOperator.CONTAINS,
            });
        }

        if(this.missingOnly) {
            filter.filters.push({
                name: 'hasMissingBillingAccount',
                value: true,
                operator: QueryOperator.EQUAL,
            });
        }

        filters.filter = filter;

        if (!cursor) {
            const data = (await this.productRepository.callContract('searchRevisionCount', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, filters)).success();
            this.pagination.totalResults = data.rows;
            this.pagination.currentPage = 1;
            this.pagination.estimateTotal = data.estimate;
        }

        filters.elementsPerPage = this.pagination.resultsPerPage;
        if (cursor && cursor.after) filters.cursorAfter = cursor.after;
        if (cursor && cursor.before) filters.cursorBefore = cursor.before;
        this.productRevisions = (await this.productRepository.callContract('searchRevision', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, filters)).success();

        this.loading = false;
    }

    nextPage() {
        if(this.loading) return;
        this.pagination.currentPage++;
        this.searchProductRevisions({after: this.productRevisions[this.productRevisions.length - 1].uid})
    }

    previousPage() {
        if(this.loading) return;
        this.pagination.currentPage--;
        this.searchProductRevisions({before: this.productRevisions[0].uid})
    }

    private searchTimeout: ReturnType<typeof setTimeout>|null = null;

    @Watch('search')
    onSearchChange() {
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }
        this.searchTimeout = setTimeout(() => {
            this.searchProductRevisions();
        }, 500);
    }

    handleKeyDown(e: KeyboardEvent) {
        this.lastKeyboardEvent = e;
        if(e.ctrlKey && e.shiftKey && e.code === 'KeyV') {
            this.showPasteModal = true;
            e.preventDefault();
        }
    }

    handleKeyUp() {
        this.lastKeyboardEvent = null;
    }

    getRevisionBillingAccount(revision: ProductRevisionNotDetailedApiOut): string|null {
        const notSavedAccount = this.productBillingAccounts.get(revision.uid);
        if(notSavedAccount) return notSavedAccount;

        const account = this.billingAccounts.get(revision.uid) ?? null;
        if(account) return account.account;

        return null;
    }

    get billingAccountDropdownValues(): DropdownValue[] {
        // Create a Map to store unique values by account name
        const uniqueValues = new Map<string, DropdownValue>();

        // Add values from billing accounts
        this.billingAccounts.forEach(billingAccount => {
            uniqueValues.set(billingAccount.account, {
                name: billingAccount.account,
                value: billingAccount.account
            });
        });

        // Add values from not saved accounts if they don't exist
        this.productBillingAccounts.forEach(account => {
            if (!uniqueValues.has(account)) {
                uniqueValues.set(account, {
                    name: account,
                    value: account
                });
            }
        });

        // Convert Map values to array
        return Array.from(uniqueValues.values());
    }

    setProductBillingAccount(productRevisionUid: VisualScopedUuid<UuidScopeProductProductRevision>, billingAccount: string | null) {
        if (billingAccount) {
            this.productBillingAccounts.set(productRevisionUid, billingAccount);
            console.log(this.productBillingAccounts);
        } else {
            this.productBillingAccounts.delete(productRevisionUid);
        }
    }

    onBillingAccountCreated(billingAccount: string) {
        if (this.showCustomValueModal) {
            this.setProductBillingAccount(this.showCustomValueModal.uid, billingAccount);
            this.showCustomValueModal = null;
        }
    }

    onBillingAccountFormClose() {
        this.showCustomValueModal = null;
    }

    async save() {
        this.saving = true;

        const tasks = Array.from(this.productBillingAccounts.entries()).map(([productRevisionUid, account]) =>
            this.billingAccountRepository.callContract('set', {
                establishmentUid: this.appState.requireUrlEstablishmentUid(),
                targetUid: productRevisionUid as VisualScopedUuid<UuidScopeProductProductRevision>,
            }, new BillingAccountApiIn({
                account: account
            }))
        );
        await Promise.all(tasks);

        this.saving = false;
        this.close();
    }

    close() {
        this.opened = false;
        setTimeout(() => this.$emit('close'), 300);
    }

    processPastedData() {
        // Reset error state
        this.pasteImportError = null;

        if (!this.pastedData.trim()) {
            this.pasteImportError = "Aucune donnée n'a été collée";
            return;
        }

        try {
            // Split the pasted data into lines
            const lines = this.pastedData.trim().split('\n');
            let errorProducts: string[] = [];

            for (const line of lines) {
                // Skip empty lines
                if (!line.trim()) continue;

                // Parse the line - expecting format: ProductName, BillingAccountName
                const match = line.match(/([^,]+),\s*(.+)/);
                if (!match) {
                    this.pasteImportError = `Format incorrect: ${line}. Format attendu: Nom produit, Nom compte comptable`;
                    return;
                }

                const [, productName, billingAccountName] = match;

                // Find the product by name
                const productRevision = this.productRevisions.find(pr =>
                    pr.name.toLowerCase() === productName.trim().toLowerCase()
                );

                if (!productRevision) {
                    errorProducts.push(productName);
                    continue;
                }

                // Set the billing account for the product
                this.setProductBillingAccount(productRevision.uid, billingAccountName.trim());
            }

            // If there were products not found, show an error
            if (errorProducts.length > 0) {
                this.pasteImportError = `Produits non trouvés: ${errorProducts.join(', ')}`;
            } else {
                // Close the paste modal if everything was successful
                this.$forceUpdate();
                this.pastedData = '';
                this.showPasteModal = false;
            }
        } catch (error) {
            this.pasteImportError = `Erreur lors du traitement des données: ${error}`;
        }
    }

    private productMap: Map<string, ProductApiOut> = new Map();

    getProductWithRevision(revision: ProductRevisionNotDetailedApiOut) {
        if (!this.productMap.size) {
            this.products.forEach(product => {
                this.productMap.set(product.uid, product);
            });
        }
        return this.productMap.get(revision.productUid) ?? null;
    }
}
