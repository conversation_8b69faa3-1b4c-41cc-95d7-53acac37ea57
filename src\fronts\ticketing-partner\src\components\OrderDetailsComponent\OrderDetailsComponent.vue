<script lang="ts" src="./OrderDetailsComponent.ts">
</script>

<style scoped lang="sass">
@use './OrderDetailsComponent.scss' as *
</style>

<template>
    <div class="order-details-component">
        <div class="loading-container" v-if="loading">
            <div class="loader"></div>
        </div>

        <template v-else>
            <div class="header">
                <div class="left">
                    <h2> N°{{ order.uid.substring(45, 51) }} </h2>
                    <span> Commande </span>
                </div>
                <dropdown-button v-if="order && order.cacheAmountPayed === order.cacheTotalAmountWithTaxesAfterDiscount"
                    title=""
                    icon="fa-regular fa-ellipsis-vertical"
                    alignment="RIGHT"
                    button-class="tertiary"
                    :actions="[{title: '', actions: [{
                        id: 'refund',
                        name: 'Rembourser',
                        icon: 'fa-regular fa-coins'
                    }, {
                        id: 'receipt',
                        name: 'Voir le reçu',
                        icon: 'fa-regular fa-receipt'
                    }]}]"
                    @clicked="dropdownClicked($event)"
                ></dropdown-button>

              <div class="displayed-receipt" v-if="receipt" @click="receipt = null">
                <img :src="receipt">
              </div>
            </div>

            <div class="date">
                {{ $filters.Day(order.creationDatetime) }}
                {{ $filters.Year(order.creationDatetime) }}
                {{ $filters.Hour(order.creationDatetime) }}
                ({{ $filters.Since(order.creationDatetime) }})
            </div>

            <div class="states">
                <div v-if="getOrderTotals.leftToPay === 0" class="label"> Payé </div>
                <div v-else class="red label"> Impayé </div>
                <!--                        <div class="clickable white label">-->
                <!--                            Facture-->
                <!--                            <i class="fa-regular fa-arrow-up-right-from-square"></i>-->
                <!--                        </div>-->
            </div>

            <div class="products">
                <template v-for="purchase in order.purchases">
                    <div class="product" v-if="getPurchaseQuantity(purchase) > 0">
                        <div class="left">
                            <product-image class="image" :establishment-uid="appState.requireUrlEstablishmentUid()" :product="orderExecutorModel.requireProductWithRevision(purchase.productRevisionUid)"></product-image>
                            <div class="infos">
                                <span class="name"> {{ $filters.Length(orderExecutorModel.requireProductRevision(purchase.productRevisionUid).name, 50) }} </span>
                                <span class="description"> Description </span>
                            </div>
                        </div>
                        <div class="values">
                            <span class="amount"> {{ $filters.Money(orderExecutorModel.getPurchasePrice(order, purchase).withTaxes) }} </span>
                            <span class="quantity"> Qté: {{ getPurchaseQuantity(purchase) }} </span>
                        </div>
                    </div>
                </template>

                <div class="total">
                    <div class="item small" v-if="getOrderTotals.purchases.discountDetails.length > 0">
                        <span class="title"> Total TTC avant remises </span>
                        <span> {{ $filters.Money(getOrderTotals.purchases.withTaxesBeforeDiscount) }} </span>
                    </div>

                    <div class="item small" v-for="discount of getOrderTotals.purchases.discountDetails">
                        <span class="title"> {{ discount.name }} </span>
                        <span v-if="'amountWithTaxes' in discount"> -{{ $filters.Money(discount.amountWithTaxes) }} </span>
                    </div>

                    <div class="item small" v-for="detail of getOrderTotals.purchases.taxDetails">
                        <span class="title"> {{ detail.descriptor.name }} {{ detail.descriptor.percent / 1000 }}% </span>
                        <span> {{ $filters.Money(detail.amount) }} </span>
                    </div>

                    <div class="item">
                        <span class="title"> Total HT  </span>
                        <span> {{ $filters.Money(getOrderTotals.purchases.withoutTaxesAfterDiscount) }} </span>
                    </div>

                    <div class="item">
                        <span class="title"> Total TTC </span>
                        <span>  {{ $filters.Money(getOrderTotals.purchases.withTaxesAfterDiscount) }} </span>
                    </div>
                </div>
            </div>

            <a target="_blank" :href="getCustomerUrl(fullOrder.customer.uid)" class="buyer" v-if="fullOrder.customer">
                <div class="profile-picture">
                    {{ fullOrder.customer.firstname ? fullOrder.customer.firstname[0] : '' }}{{ fullOrder.customer.lastname ? fullOrder.customer.lastname[0] : '' }}
                </div>
                <div class="data">
                    <span class="name">
                        {{ fullOrder.customer.firstname }}
                        {{ fullOrder.customer.lastname }}
                    </span>
                    <span class="email">
                        {{ fullOrder.customer.email }}
                    </span>
                </div>
                <i class="fa-regular fa-arrow-up-right-from-square"></i>
            </a>

            <div class="metadata" v-for="metadata of order.metadataList">
                <div class="name"> {{ requireMetadataDescriptorWithUid(metadata.descriptorUid).descriptorTemplate.name }}</div>
                <div class="value"> {{ $filters.metadataValue(metadata.value) }} </div>
            </div>

            <template v-if="fullOrder.tickets.length > 0">
                <h3 class="separator-header"> Billets </h3>

                <div class="tickets">
                    <div class="ticket" v-for="ticket of fullOrder.tickets">
                        <div class="ticket-icon">
                            <i class="fa-regular fa-ticket-simple"></i>
                        </div>

                        <div class="ticket-name">
                            {{ ticket.templateTicketName }}
                        </div>

                        <div class="red label" v-if="ticket.disabled">
                            Annulé
                        </div>

                        <dropdown-button
                            button-class="grey button"
                            icon="fa-regular fa-ellipsis-vertical"
                            title=""
                            alignment="RIGHT"
                            @clicked="ticketDropdownClicked(ticket, $event)"
                            :actions="[{title: '', actions: [{
                                id: 'scan',
                                name: 'Scanner le billet',
                                icon: 'fa-regular fa-arrow-right-to-bracket'
                            }, {
                                id: 'open',
                                name: 'Voir le PDF',
                                icon: 'fa-regular fa-arrow-up-right-from-square'
                            }]}]"
                        ></dropdown-button>
                    </div>
                </div>
            </template>

            <h3 class="separator-header"> Paiements </h3>

            <div v-if="order.payments.length === 0">
                Aucun paiement
            </div>
            <div class="payments">
                <div class="payment" v-for="payment in order.payments">
                    <div class="left">
                        <span class="hour"> {{ $filters.Hour(payment.creationDatetime) }} </span>
                        <div>
                            <div class="label" :class="{red: payment.status === 'ERROR'}"> {{ translatePaymentStatus(payment) }} </div>
                        </div>
                    </div>
                    <div class="right">
                        <span class="amount"> {{ $filters.Money(payment.amount) }} </span>
                    </div>
                </div>
            </div>

            <template v-if="fullOrder.orderRefundList.length > 0">
                <h3 class="separator-header"> Remboursements </h3>

                <div class="refunds">
                    <div class="refund" v-for="refund of fullOrder.onlinePaymentRefundList">
                        <div class="top">
                            <div class="left">
                                <span class="hour"> {{ $filters.Hour(refund.creationDatetime) }} </span>

                                <div class="label" :class="{red: refund.status === 'ERROR', grey: refund.status === 'CREATED'}"> {{ translateRefundStatus(refund) }} </div>

                                <span class="type"> {{ translateRefundProtocol(refund) }} </span>
                            </div>
                            <div class="right">
                                <span class="amount"> {{ $filters.Money(refund.amount) }} </span>

                                <div class="test-buttons" v-if="refund.protocol === 'WEB_TEST' && refund.status === 'CREATED'">
                                    <button class="small green button" @click="setTestRefundStatus(refund, true)">
                                        <i class="fa-regular fa-check fa-fw"></i>
                                    </button>
                                    <button class="small red button" @click="setTestRefundStatus(refund, false)">
                                        <i class="fa-regular fa-xmark fa-fw"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <span class="comment" v-if="requireRefundWithOnline(refund.uid).comment">
                            {{ requireRefundWithOnline(refund.uid).comment }}
                        </span>
                    </div>
                </div>
            </template>

<!--            <div class="actions">-->
<!--                <button class="white button" @click="showRefundForm = true">-->
<!--                    <i class="fa-regular fa-coins"></i>-->
<!--                    Rembourser-->
<!--                </button>-->

<!--                <button class="white button">-->
<!--                    <i class="fa-regular fa-reply"></i>-->
<!--                    Renvoyer l’email de confirmation-->
<!--                </button>-->
<!--            </div>-->
        </template>

        <order-refund
            v-if="showRefundForm"
            :full-order="fullOrder"
            :order-executor-model="orderExecutorModel"
            @close="showRefundForm = false"
        ></order-refund>

        <ticket-validate-form
            v-if="showTicketValidateForm"
            :ticket="showTicketValidateForm"
            @close="showTicketValidateForm = null"
        ></ticket-validate-form>
    </div>
</template>