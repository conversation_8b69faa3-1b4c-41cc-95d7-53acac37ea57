import {Component, Prop, Vue} from "vue-facing-decorator";
import {
	MetadataDescriptorApiOut, OnlinePaymentRefundTestApiOut, OnlinePaymentTestRefundApiIn,
	OrderApiOut,
	OrderDependenciesApiOut,
	OrderExecutorModel, PaymentUniformizedErrors,
	ProductEstablishmentApiOut,
	PurchaseApiOut,
	TicketApiOut,
	UuidScopeCustomer_customer,
	UuidScopeMetadata_descriptor, uuidScopePayment_onlinePaymentRefund,
	UuidScopeProductProduct,
} from "@groupk/mastodon-core";
import {AutoWired, sinkNever, UuidUtils, VisualScopedUuid} from "@groupk/horizon2-core";
import {OrderRepository} from "../../../../../shared/repositories/OrderRepository";
import ProductImageComponent from "../../components/ProductImageComponent/ProductImageComponent.vue";
import {DropdownButtonAction, DropdownButtonComponent} from "@groupk/vue3-interface-sdk";
import {
	OrderReceiptRender,
	OrderReceiptRenderConfig
} from "../../../../../shared/mastodonCoreFront/product/OrderReceiptRender";
import {PrinterRepository} from "../../../../../shared/mastodonCoreFront/PrinterRepository";
import {PaymentMethodRepository} from "../../../../../shared/repositories/PaymentMethodRepository";
import {ProductEstablishmentRepository} from "../../../../../shared/repositories/ProductEstablishmentRepository";
import {EstablishmentUrlBuilder} from "../../../../../shared/utils/EstablishmentUrlBuilder";
import TicketValidateFormComponent from "../TicketValidateFormComponent/TicketValidateFormComponent.vue";
import {MainConfig} from "../../../../../shared/MainConfig";
import {AppState} from "../../../../../shared/AppState";
import OrderRefundComponent from "../OrderRefundComponent/OrderRefundComponent.vue";
import {
	OnlinePaymentRefundApiOut,
	OrderPaymentApiOut,
	OrderPaymentStatus, PaymentMethodDataWebRefund, PaymentProtocol, PaymentStatus, UuidScopePayment_onlinePaymentRefund
} from "@groupk/mastodon-core";
import {TestPaymentRepository} from "../../../../../shared/repositories/TestPaymentRepository";
import {AppBus} from "../../AppBus";

@Component({
	components: {
		'product-image': ProductImageComponent,
		'dropdown-button': DropdownButtonComponent,
		'ticket-validate-form': TicketValidateFormComponent,
		'order-refund': OrderRefundComponent,
	},
	emits: [],
})
export default class OrderDetailsComponent extends Vue {
	@Prop({required: true}) order!: OrderApiOut;
	@Prop({required: true}) orderExecutorModel!: OrderExecutorModel;
	@Prop({required: true}) metadataDescriptors!: MetadataDescriptorApiOut[];

	fullOrder!: OrderDependenciesApiOut;

	showTicketValidateForm: TicketApiOut|null = null;
	showRefundForm: boolean = false;
	productEstablishment: ProductEstablishmentApiOut|null = null;
	receipt: null|string = null;
	loading: boolean = true;

	@AutoWired(OrderRepository) accessor orderRepository!: OrderRepository;
	@AutoWired(PaymentMethodRepository) accessor paymentMethodRepository!: PaymentMethodRepository;
	@AutoWired(MainConfig) accessor mainConfig!: MainConfig;
	@AutoWired(PrinterRepository) accessor printerRepository!: PrinterRepository;
	@AutoWired(ProductEstablishmentRepository) accessor productEstablishmentRepository!: ProductEstablishmentRepository;
	@AutoWired(TestPaymentRepository) accessor testPaymentRepository!: TestPaymentRepository;
	@AutoWired(AppState) accessor appState!: AppState;
	@AutoWired(AppBus) accessor appBus!: AppBus;

	async mounted() {
		this.fullOrder = (await this.orderRepository.callContract('getOneDependencies', {establishmentUid: this.appState.requireUrlEstablishmentUid(), orderUid: this.order.uid}, undefined)).success()
		this.orderExecutorModel.registerProducts(this.fullOrder.products);
		this.orderExecutorModel.registerProductRevisions(this.fullOrder.productRevisions);
		this.loading = false;
	}

	get getOrderTotals(){
		return this.orderExecutorModel.getOrderTotals(this.order);
	}

	getPurchaseQuantity(purchase: PurchaseApiOut) {
		return purchase.items.reduce((quantity, item) => {
			const isItemCanceled = this.orderExecutorModel.isPurchaseItemCanceled(item);
			return quantity + (isItemCanceled ? 0 : item.quantity);
		}, 0);
	}

	getProductImageUrl(productUid: VisualScopedUuid<UuidScopeProductProduct>) {
		return this.mainConfig.configuration.mastodonApiEndpoint + `/Public/Product/v1/Establishments/${this.appState.requireUrlEstablishmentUid()}/Products/${productUid}/Images/Main/contain/128/2024-01-18T20:30:20Z/b=0/image.png`
	}

	dropdownClicked(action: DropdownButtonAction) {
		if(action.id === 'refund') this.showRefundForm = true;
		if(action.id === 'receipt') this.printReceipt();
	}

	ticketDropdownClicked(ticket: TicketApiOut, action: DropdownButtonAction) {
		if(action.id === 'open') window.open(this.getTicketPdfUrl(ticket), '_blank')?.focus();
		if(action.id === 'scan') this.showTicketValidateForm = ticket;
	}

	async printReceipt() {
		const orderReceiptRender = new OrderReceiptRender(this.orderExecutorModel);
		let paymentMethods = (await this.paymentMethodRepository.callContract('list', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();
		this.orderExecutorModel.registerPaymentMethods(paymentMethods);

		const receiptConfig: OrderReceiptRenderConfig = {
			companyAddress: '',
			companyName: 'Caisse by Weecop',
			posVersion: '1.0.32',
			identifiers: {siren: '', vatNumber: ''}
		};

		if(!this.productEstablishment) {
			this.productEstablishment = (await this.productEstablishmentRepository.callContract('get', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();
		}

		if(this.productEstablishment && this.productEstablishment.receipt) {
			receiptConfig.companyAddress = this.productEstablishment.receipt.companyAddress;
			receiptConfig.companyName = this.productEstablishment.receipt.companyName;
			receiptConfig.identifiers.vatNumber = this.productEstablishment.receipt.vatNumber ?? undefined;
			receiptConfig.identifiers.siren = this.productEstablishment.receipt.siren ?? '';
			receiptConfig.freeLinesTop = this.productEstablishment.receipt.freeAreaTop;
			receiptConfig.freeLinesBottom = this.productEstablishment.receipt.freeAreaBottom;
		}

		const parts = orderReceiptRender.renderOrderReceipt(this.order, receiptConfig);
		if(parts) this.receipt = await this.printerRepository.printToBase64(parts);
	}

	translatePaymentStatus(payment: OrderPaymentApiOut) {
		if(payment.status === OrderPaymentStatus.ERROR) {
			return 'Erreur';
		} else if(payment.status === OrderPaymentStatus.PENDING) {
			return 'En cours';
		} else if(payment.status === OrderPaymentStatus.SUCCESS) {
			return 'Succès';
		} else {
			sinkNever(payment.status);
		}
	}

	translateRefundStatus(refund: OnlinePaymentRefundApiOut) {
		if(refund.status === PaymentStatus.ERROR) {
			return 'Erreur';
		} else if(refund.status === PaymentStatus.CREATED) {
			return 'En cours';
		} else if(refund.status === PaymentStatus.SUCCESS) {
			return 'Succès';
		} else {
			sinkNever(refund.status);
		}
	}

	translateRefundProtocol(refund: OnlinePaymentRefundApiOut) {
		if(refund.protocol === PaymentProtocol.WEB_TEST) {
			return 'TEST';
		} else if(refund.protocol === PaymentProtocol.WEB_STRIPE) {
			return 'Stripe';
		}
		return refund.protocol;
	}

	getCustomerUrl(customerUid: VisualScopedUuid<UuidScopeCustomer_customer>) {
		return EstablishmentUrlBuilder.buildUrl('/customers?uid=' + UuidUtils.visualToScoped(customerUid));
	}

	getTicketPdfUrl(ticket: TicketApiOut) {
		return this.mainConfig.configuration.mastodonApiEndpoint +  `/Product/v1/Establishments/${this.appState.requireUrlEstablishmentUid()}/Tickets/${ticket.uid}/PDF`
	}

	requireMetadataDescriptorWithUid(descriptorUid: VisualScopedUuid<UuidScopeMetadata_descriptor>) {
		const descriptor = this.metadataDescriptors.find((descriptor) => descriptor.uid === descriptorUid);
		if (!descriptor) throw new Error('missing_descriptor');
		return descriptor;
	}

	requireRefundWithOnline(uid: VisualScopedUuid<UuidScopePayment_onlinePaymentRefund>) {
		const refund = this.fullOrder.orderRefundList.find((refund) => {
			return refund.methodData instanceof PaymentMethodDataWebRefund &&
				UuidUtils.scopedToVisual(refund.methodData.onlinePaymentRefundUid, uuidScopePayment_onlinePaymentRefund) === uid;
		});
		if(!refund) throw new Error('missing_refund');
		return refund;
	}

	async setTestRefundStatus(refund: OnlinePaymentRefundApiOut, success: boolean) {
		let apiIn = new OnlinePaymentTestRefundApiIn({
			status: PaymentStatus.SUCCESS,
			errorCode: PaymentUniformizedErrors.NONE,
		});
		if(!success) {
			apiIn = new OnlinePaymentTestRefundApiIn({
				status: PaymentStatus.ERROR,
				errorCode: PaymentUniformizedErrors.CANCELED,
			});
		}

		const response = await this.testPaymentRepository.callContract('setRefundStatus', {
			establishmentUid: this.appState.requireUrlEstablishmentUid(),
			onlinePaymentUid: refund.onlinePaymentUid,
			refundUid: refund.uid,
		}, apiIn);

		if(response.isSuccess()) {
			const updatedRefund = response.success();
			const index = this.fullOrder.onlinePaymentRefundList.findIndex((refund) => refund.uid === updatedRefund.uid);
			if(index !== -1) this.fullOrder.onlinePaymentRefundList.splice(index, 1, updatedRefund);
		} else {
			this.appBus.emit('emit-toast', {
				title: 'Erreur',
				description: 'Une erreur est survenue',
				duration: 3000,
				type: 'ERROR',
				closable: true
			});
		}
	}
}