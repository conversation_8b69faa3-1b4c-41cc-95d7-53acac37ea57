.order-details-component {
    display: flex;
    flex-direction: column;
    gap: 20px;

    .header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        gap: 20px;

        .left {
            display: flex;
            flex-direction: column;

            h2 {
                margin: 0;
                font-size: 24px;
                font-weight: 700;
            }

            span {
                font-size: 14px;
                font-weight: 400;
            }
        }

        .button {
            font-weight: 500;
            font-size: 15px;
            padding: 10px 20px;

            i {
                font-size: 14px;
            }
        }
    }

    .date {
        font-size: 15px;
    }

    .states {
        display: flex;
        gap: 10px;
    }


    .products {
        display: flex;
        flex-direction: column;
        gap: 20px;
        background: var(--secondary-hover-color);
        padding: 15px;
        border-radius: 12px;

        .product {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 10px;

            .left {
                display: flex;
                align-items: center;
                gap: 10px;

                .image {
                    height: 64px;
                    width: 64px;
                    border-radius: 8px;
                    overflow: hidden;
                }

                .infos {
                    display: flex;
                    flex-direction: column;
                    gap: 4px;

                    .name {
                        font-size: 14px;
                        font-weight: 600;
                    }

                    .description {
                        font-size: 12px;
                    }
                }

            }

            .values {
                display: flex;
                flex-direction: column;
                gap: 4px;

                .amount {
                    font-size: 14px;
                    font-weight: 600;
                }

                .quantity {
                    font-size: 12px;
                }
            }
        }

        .total {
            display: flex;
            flex-direction: column;
            gap: 10px;
            font-size: 16px;

            .item {
                display: flex;
                justify-content: space-between;

                .title {
                    font-size: 14px;
                }

                &.small {
                    font-weight: 400;
                }
            }
            .left {
                text-align: left;
            }

            font-weight: 700;
        }
    }

    .buyer {
        all: unset;
        display: flex;
        align-items: center;
        gap: 15px;
        background: var(--secondary-hover-color);
        padding: 15px;
        border-radius: 12px;
        cursor: pointer;

        .profile-picture {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 38px;
            width: 38px;
            background: #D9D9D9;
            border-radius: 50%;
            text-transform: uppercase;
            font-weight: 500;
            font-size: 13px;
        }

        .data {
            flex-grow: 2;
            display: flex;
            flex-direction: column;
            gap: 4px;

            .name {
                font-size: 14px;
                font-weight: 500;
            }

            .email {
                font-size: 14px;
                word-break: break-all;
            }
        }

        i {
            font-size: 18px;
            margin-right: 5px;
        }
    }

    .metadata {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .name {
            font-size: 14px;
            font-weight: 500;
        }

        .value {
            font-size: 14px;
        }
    }

    .tickets {
        display: flex;
        flex-direction: column;
        background: var(--secondary-hover-color);
        gap: 15px;
        padding: 15px;
        border-radius: 12px;

        .ticket {
            all: unset;
            display: flex;
            align-items: center;
            gap: 15px;
            border-bottom: 1px solid var(--border-color);
            padding: 0 0 15px 0;
            cursor: pointer;

            &:last-child {
                border: none;
                padding: 0;
            }

            .ticket-icon {
                display: flex;
                justify-content: center;
                align-items: center;
                height: 32px;
                width: 32px;
                background: #D9D9D9;
                border-radius: 4px;
                flex-shrink: 0;
            }

            .ticket-name {
                flex-grow: 2;
                word-break: break-all;
            }

            .used {
                font-size: 14px;
            }
        }
    }


    .separator-header {
        font-size: 16px;
        font-weight: 700;
        margin: 0;
    }

    .payments {
        display: flex;
        flex-direction: column;
        gap: 15px;

        .payment {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .left {
                display: flex;
                align-items: center;
                gap: 10px;

                .hour {
                    width: 40px;
                    font-size: 12px;
                }
            }

            .right {
                .amount {
                    font-size: 14px;
                    font-weight: 600;
                }
            }
        }
    }

    .refunds {
        display: flex;
        flex-direction: column;
        gap: 15px;

        .refund {
            display: flex;
            flex-direction: column;
            gap: 4px;

            .top {
                display: flex;
                justify-content: space-between;
                align-items: center;

                .left {
                    display: flex;
                    align-items: center;
                    gap: 10px;

                    .hour {
                        width: 40px;
                        font-size: 12px;
                    }

                    .type {
                        font-size: 12px;
                    }
                }

                .right {
                    display: flex;
                    align-items: center;
                    gap: 10px;

                    .amount {
                        font-size: 14px;
                        font-weight: 600;
                    }

                    .test-buttons {
                        display: flex;
                        gap: 5px;

                        .button {
                            padding: 10px;
                        }
                    }
                }
            }

            .comment {
                font-size: 14px;
            }

        }
    }

    .actions {
        display: flex;
        flex-direction: column;
        gap: 20px;
        margin-top: 20px;
    }

    .displayed-receipt {
        position: fixed;
        inset: 0;
        z-index: 999;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: flex-start;
        justify-content: center;
        padding: 40px;
        overflow: auto;

        img {
            width: 400px;
        }
    }
}