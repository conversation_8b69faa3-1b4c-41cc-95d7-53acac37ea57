import {Component, Vue} from "vue-facing-decorator";
import {
	AutoWired,
	ScopedUuid,
	TypedQuerySearch,
	Uuid,
	UuidUtils,
	VisualScopedUuid
} from "@groupk/horizon2-core";
import {
	MetadataDescriptorApiOut,
	PermissionBuilder, uuidScopeCustomer_customer,
	uuidScopeEstablishment,
	UuidScopeEstablishment, uuidScopeProduct_order, uuidScopeProductCashStatementSession,
	ApplicationPermission,
	EstablishmentAccountPermissionModel,
	PaymentHttpMethodContract,
	ProductHttpPurchaseStatusContract,
	ProductHttpBillingRegionContract,
	MetadataHttpCustomerDescriptorContract, ProductHttpOrdersContract, ProductHttpEventContract
} from "@groupk/mastodon-core";
import {ProductRepository} from "../../../../../shared/repositories/ProductRepository";
import {TableColumnsRepository} from "../../../../../shared/repositories/TableColumnsRepository";
import SidebarStateListener from "../../../../../shared/utils/SidebarStateListener";
import {Router} from "@groupk/horizon2-front";
import {
	OrderApiOut,
	ProductHttpOrderContractSearchConfig,
	OrderExecutorModel,
} from "@groupk/mastodon-core";
import {
	FilterTableLayoutComponent,
	ContentHeaderParameters,
	TableColumn,
	TablePagination,
	DropdownButtonComponent,
	SavedFilter,
	ShortcutsManager, FilterParameters,
	ForbiddenMessageComponent
} from "@groupk/vue3-interface-sdk";
import {OrderRepository} from "../../../../../shared/repositories/OrderRepository";
import {PurchaseStatusRepository} from "../../../../../shared/repositories/PurchaseStatusRepository";
import {SavedFiltersRepository} from "../../../../../shared/repositories/SavedFiltersRepository";
import {BillingRegionRepository} from "../../../../../shared/repositories/BillingRegionRepository";
import OrderDetailsComponent from "../../components/OrderDetailsComponent/OrderDetailsComponent.vue";
import {randomUUID} from "@groupk/horizon2-front";
import {ViewRouteStateAble} from "@groupk/horizon2-front";

import {AppState} from "../../../../../shared/AppState";
import {MainConfig} from "../../../../../shared/MainConfig";
import {MetadataDescriptorRepository} from "../../../../../shared/repositories/MetadataDescriptorRepository";
import SellsExportComponent, {
	SellsExportComponentHasRequiredPermissions
} from "../../../../../shared/components/ProductSellsExportComponent/SellsExportComponent.vue";
import {QueryFilterUtils} from "../../../../../shared/utils/QueryFilterUtils";
import {ComponentUtils} from "../../../../../shared/utils/ComponentUtils";
import ToastManagerComponent from "../../components/ToastManagerComponent/ToastManagerComponent.vue";

@Component({
	components: {
		'filter-table-layout': FilterTableLayoutComponent,
		'dropdown-button': DropdownButtonComponent,
		'order-details': OrderDetailsComponent,
		'sells-export': SellsExportComponent,
		'forbidden-message': ForbiddenMessageComponent,
		'toast-manager': ToastManagerComponent
	}
})
export default class OrdersView extends Vue implements ViewRouteStateAble<undefined> {
	establishmentUid!: VisualScopedUuid<UuidScopeEstablishment>;


	orders: OrderApiOut[] = [];
	metadataDescriptors: MetadataDescriptorApiOut[] = [];
	selectedOrder: OrderApiOut|null = null;
	loading: boolean = true;
	showExportModal: boolean = false;
	forbidden: boolean = false;

	headerParameters: ContentHeaderParameters = {
		header: 'Commandes',
		subtitle: 'Liste des commandes passées sur votre établissement',
		actions: [{
			id: 'export-sells',
			type: 'SIMPLE_ACTION',
			name: 'Exporter en CSV',
			icon: 'fa-regular fa-arrow-down-to-line',
			callback: this.toggleExportModal
		}],
		hideSearch: true,
		searchPlaceholder: 'Rechercher une commande'
	}

	allowedFilters = ProductHttpOrderContractSearchConfig;
	filters: TypedQuerySearch<typeof ProductHttpOrderContractSearchConfig> = {};
	appliedFilters: TypedQuerySearch<typeof ProductHttpOrderContractSearchConfig> = {};
	savedFilters: SavedFilter[] = [];

	tableKey = 'product-orders';
	tableColumns: TableColumn[] = [{
		title: 'ID', name: 'uid', displayed: true, mobileHidden: true
	}, {
		title: 'Total TTC', name: 'total', displayed: true, mobileHidden: false
	}, {
		title: 'Status', name: 'status', displayed: true, mobileHidden: true
	}, {
		title: 'Date', name: 'creationDatetime', displayed: true, mobileHidden: true
	}];

	filterParameters: {
		[filterName: string]: FilterParameters
	} = {
		'uid': {
			translation: 'Uid (visual)',
			type: 'UNKNOWN',
			validation: (value: unknown) => {
				return typeof value === 'string' && UuidUtils.isVisual(value, uuidScopeProduct_order);
			}
		},
		'total': {
			translation: 'Total',
			type: 'DECIMAL',
			transformFunction: QueryFilterUtils.priceTransformFunction(),
			validation: QueryFilterUtils.amountValidation
		},
		'creationDatetime': {
			translation: 'Date de création',
			type: 'DATETIME',
			validation: QueryFilterUtils.dateValidation
		},
		'fullyPaid': {
			translation: 'Totalement payé',
			type: 'BOOLEAN'
		},
		'cashStatementSessionUid': {
			translation: 'Uid de session (visual)',
			type: 'UNKNOWN',
			validation: (value: unknown) => {
				return typeof value === 'string' && UuidUtils.isVisual(value, uuidScopeProductCashStatementSession);
			}
		},
		'customerUid': {
			translation: 'Uid du client (visual)',
			type: 'UNKNOWN',
			validation: (value: unknown) => {
				return typeof value === 'string' && UuidUtils.isVisual(value, uuidScopeCustomer_customer);
			}
		},
	}

	pagination: TablePagination = {
		totalResults: 0,
		resultsPerPage: 50,
		currentPage: 1,
		estimateTotal: false
	}

	orderExecutorModel!: OrderExecutorModel;

	@AutoWired(ProductRepository) accessor productRepository!: ProductRepository;
	@AutoWired(BillingRegionRepository) accessor billingRegionRepository!: BillingRegionRepository;
	@AutoWired(PurchaseStatusRepository) accessor purchaseStatusRepository!: PurchaseStatusRepository;
	@AutoWired(MetadataDescriptorRepository) accessor metadataDescriptorRepository!: MetadataDescriptorRepository;
	@AutoWired(OrderRepository) accessor orderRepository!: OrderRepository;
	@AutoWired(TableColumnsRepository) accessor tableColumnsRepository!: TableColumnsRepository;
	@AutoWired(SavedFiltersRepository) accessor savedFiltersRepository!: SavedFiltersRepository
	@AutoWired(ShortcutsManager) accessor shortcutsManager!: ShortcutsManager;
	@AutoWired(SidebarStateListener) accessor sidebarStateListener!: SidebarStateListener;
	@AutoWired(AppState) accessor appState!: AppState;
	@AutoWired(Router) accessor router!: Router;
	@AutoWired(MainConfig) accessor mainConfig!: MainConfig;

	beforeMount() {
		this.sidebarStateListener.setHiddenSidebar(false);
		this.sidebarStateListener.setMinimizedSidebar(false);

		let regexMatch = this.router.lastRouteRegexMatches;

		if (regexMatch && regexMatch[1]) {
			this.establishmentUid = UuidUtils.scopedToVisual<UuidScopeEstablishment>(regexMatch[1] as ScopedUuid<UuidScopeEstablishment>, uuidScopeEstablishment);
		}

		let savedPreferences = this.tableColumnsRepository.getColumnPreferences(this.tableKey, this.tableColumns);
		if(savedPreferences) this.tableColumns = savedPreferences;

		this.savedFilters = this.savedFiltersRepository.getFilters(this.tableKey) ?? [];


		if(!ComponentUtils.hasPermissions(SellsExportComponentHasRequiredPermissions)) {
			ComponentUtils.disableActions(this.headerParameters, ['export-sells']);
		}
	}

	load() {
		return {
			pageTitle: 'Commandes - Billetterie'
		};
	}

	async mounted() {
		// Check for essential page functionality
		if(!ComponentUtils.hasPermissions((ownedPermissions: ApplicationPermission[]) => {
			return EstablishmentAccountPermissionModel.hasPermissionsForContracts(ownedPermissions, [
				ProductHttpOrdersContract.search,
				ProductHttpOrdersContract.searchCount,
				PaymentHttpMethodContract.list,
				ProductHttpPurchaseStatusContract.list,
				ProductHttpBillingRegionContract.list,
				MetadataHttpCustomerDescriptorContract.listDescriptors,
			])
		})) {
			this.forbidden = true;
			return;
		}

		const billingRegions = (await this.billingRegionRepository.callContract('list', {establishmentUid: this.establishmentUid}, {})).success();
		const statuses = (await this.purchaseStatusRepository.callContract('list', {establishmentUid: this.establishmentUid}, {})).success();
		this.metadataDescriptors = (await this.metadataDescriptorRepository.callContract('listDescriptors', {establishmentUid: this.appState.requireUrlEstablishmentUid()}, undefined)).success();
		this.orderExecutorModel = new OrderExecutorModel<undefined>({
			randomUUID: randomUUID,
			purchaseStatuses: statuses,
			billingRegions: billingRegions
		});

		this.searchOrders(this.filters);

		this.setupShortcutListeners();
	}

	beforeUnmount() {
		this.shortcutsManager.off('ArrowUp', this.handleKeypress);
		this.shortcutsManager.off('ArrowDown', this.handleKeypress);
		this.shortcutsManager.off('Escape', this.handleKeypress);
	}

	setupShortcutListeners() {
		this.shortcutsManager.on('ArrowUp', this.handleKeypress);
		this.shortcutsManager.on('ArrowDown', this.handleKeypress);
		this.shortcutsManager.on('Escape', this.handleKeypress);
	}

	handleKeypress(e: KeyboardEvent) {
		if(e.code === 'ArrowUp' || e.code === 'ArrowDown') {
			if(!this.selectedOrder) {
				this.selectedOrder = this.orders[0] ?? null;
			} else {
				const currentIndex = this.orders.findIndex((order) => order.uid === this.selectedOrder?.uid);

				if (e.code === 'ArrowUp') {
					if(currentIndex > 0) {
						this.selectedOrder = this.orders[currentIndex - 1];
					}
				} else if (e.code === 'ArrowDown') {
					if(currentIndex < this.orders.length - 1) {
						this.selectedOrder = this.orders[currentIndex + 1];
					}
				}
			}
		} else if(e.code === 'Escape') {
			this.selectedOrder = null;
		}
	}

	toggleExportModal() {
		this.showExportModal = true;
	}


	async searchOrders(filters: TypedQuerySearch<typeof ProductHttpOrderContractSearchConfig>, cursor: {after?: Uuid, before?: Uuid}|null = null) {
		this.loading = true;
		this.filters = {...filters};

		if(!cursor) {
			const data = (await this.orderRepository.callContract('searchCount', {establishmentUid: this.establishmentUid}, filters)).success();
			this.pagination.totalResults = data.rows;
			this.pagination.currentPage = 1;
			this.pagination.estimateTotal = data.estimate;
		}

		filters.elementsPerPage = this.pagination.resultsPerPage;
		if(cursor && cursor.after) filters.cursorAfter = cursor.after;
		if(cursor && cursor.before) filters.cursorBefore = cursor.before;
		this.orders = (await this.orderRepository.callContract('search', {establishmentUid: this.establishmentUid}, filters)).success();
		this.appliedFilters = filters;
		this.loading = false;
	}

	saveColumnPreferences(columns: TableColumn[]) {
		this.tableColumnsRepository.saveColumnsPreferences(this.tableKey, columns);
	}

	sorted(data: { name: string, direction: 'asc'|'desc' }) {
	}

	nextPage() {
		this.pagination.currentPage++;
		this.searchOrders(this.filters, {after: this.orders[this.orders.length - 1].uid})
	}

	previousPage() {
		this.pagination.currentPage--;
		this.searchOrders(this.filters, {before: this.orders[0].uid})
	}

	toggleSelectedOrder(order: OrderApiOut) {
		const selection = window.getSelection();
		if (selection && !selection.isCollapsed) {
			return;
		}
		if(this.selectedOrder && this.selectedOrder.uid === order.uid) this.selectedOrder = null;
		else this.selectedOrder = order;
	}

	saveFilter(name: string) {
		if(this.filters.filter) {
			this.savedFilters.push({
				name: name,
				filter: this.filters.filter
			});
			this.savedFiltersRepository.saveFilters(this.tableKey, this.savedFilters);
		}
	}

	selectFilter(savedFilter: SavedFilter) {
		this.filters.filter = savedFilter.filter as any;
		this.searchOrders(this.filters);
	}

	deleteFilter(index: number) {
		this.savedFilters.splice(index, 1);
		this.savedFiltersRepository.saveFilters(this.tableKey, this.savedFilters);
	}
}