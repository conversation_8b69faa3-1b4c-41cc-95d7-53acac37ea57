<script lang="ts" src="./orders.ts" />

<style scoped lang="sass">
@use './orders.scss' as *
</style>

<template>
    <div id="orders-page" class="page">
        <forbidden-message v-if="forbidden"></forbidden-message>

        <filter-table-layout
            v-else
            :header-parameters="headerParameters"
            :allowed-filters="allowedFilters"
            :table-columns="tableColumns"
            :filters="filters"
            :applied-filters="appliedFilters"
            :drawer-opened="selectedOrder !== null"
            :pagination="pagination"
            :saved-filters="savedFilters"
            :filter-parameters="filterParameters"
            @filters-changed="searchOrders($event);"
            @next="nextPage()"
            @previous="previousPage()"
            @sorted="sorted($event)"
            @changed-column-preferences="saveColumnPreferences($event)"
            @save-filter="saveFilter($event)"
            @select-filter="selectFilter($event)"
            @delete-filter="deleteFilter($event)"
        >
            <template v-slot:table-data>
                <tr class="table-dimmer" :class="{relative: orders.length === 0}" v-if="loading">
                    <td colspan="100%">
                        <div class="dimmer">
                            <div class="loader-container">
                                <div class="loader"></div>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr class="table-no-data" v-else-if="orders.length === 0">
                    <td colspan="100%">Aucune donnée</td>
                </tr>
                <tr v-for="order in orders" :class="{selected: selectedOrder && order.uid === selectedOrder.uid}">
                    <td :class="{'mobile-hidden': column.mobileHidden}" v-for="column in tableColumns.filter((instance) => instance.displayed)" @click="toggleSelectedOrder(order)">
                        <template v-if="column.name === 'uid'"> {{ order.uid.substring(45, 51) }} </template>
                        <template v-if="column.name === 'total'"> {{ $filters.Money(order.cacheTotalAmountWithTaxesAfterDiscount) }} </template>
                        <template v-if="column.name === 'status'">
                            <div v-if="order.cacheAmountPayed === order.cacheTotalAmountWithTaxesAfterDiscount" class="label"> Payé </div>
                            <div v-else class="red label"> Impayé </div>
                        </template>
                        <template v-if="column.name === 'creationDatetime'">
                            {{ $filters.Date(order.creationDatetime) }}
                            {{ $filters.Hour(order.creationDatetime) }}
                            ({{ $filters.Since(order.creationDatetime) }})
                        </template>
                    </td>
                </tr>
            </template>
            <template v-slot:right>
                <div v-if="!selectedOrder" class="empty-right-panel">
                    <img :src="$assets.selectHint" />
                    Cliquez sur une commande pour <br/> le sélectionner
                </div>
                <div v-else class="selected-order">
                    <div class="close" @click="toggleSelectedOrder(selectedOrder)">
                        <i class="fa-regular fa-xmark"></i>
                        <span> Fermer </span>
                    </div>

                    <order-details
                        :key="selectedOrder.uid"
                        :establishment-uid="establishmentUid"
                        :order="selectedOrder"
                        :order-executor-model="orderExecutorModel"
                        :metadata-descriptors="metadataDescriptors"
                    ></order-details>
                </div>
            </template>
        </filter-table-layout>

        <sells-export
            v-if="showExportModal"
            :establishment-uid="establishmentUid"
            @close="showExportModal = false"
        ></sells-export>
    </div>

    <toast-manager></toast-manager>
</template>